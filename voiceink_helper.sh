#!/bin/bash

# VoiceInk Helper Script
# Provides convenient commands for managing the VoiceInk application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# App path
APP_PATH="/Users/<USER>/Library/Developer/Xcode/DerivedData/VoiceInk-gspeyufcasgzvjddsgcxjmtmthxq/Build/Products/Debug/VoiceInk.app"

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if VoiceInk is running
check_status() {
    if pgrep -f "VoiceInk.app" > /dev/null; then
        log_success "VoiceInk is running"
        ps aux | grep VoiceInk | grep -v grep | head -1
        return 0
    else
        log_warning "VoiceInk is not running"
        return 1
    fi
}

# Start VoiceInk
start_app() {
    log_info "Starting VoiceInk..."
    if check_status > /dev/null 2>&1; then
        log_warning "VoiceInk is already running"
        return 0
    fi
    
    if [[ -d "$APP_PATH" ]]; then
        open "$APP_PATH"
        sleep 2
        if check_status > /dev/null 2>&1; then
            log_success "VoiceInk started successfully"
        else
            log_error "Failed to start VoiceInk"
            return 1
        fi
    else
        log_error "VoiceInk app not found at: $APP_PATH"
        log_info "You may need to rebuild the app first"
        return 1
    fi
}

# Stop VoiceInk
stop_app() {
    log_info "Stopping VoiceInk..."
    if ! check_status > /dev/null 2>&1; then
        log_warning "VoiceInk is not running"
        return 0
    fi
    
    pkill -f "VoiceInk.app" || true
    sleep 1
    
    if ! check_status > /dev/null 2>&1; then
        log_success "VoiceInk stopped successfully"
    else
        log_warning "VoiceInk may still be running"
    fi
}

# Restart VoiceInk
restart_app() {
    log_info "Restarting VoiceInk..."
    stop_app
    sleep 1
    start_app
}

# Show app info
show_info() {
    log_info "VoiceInk Application Information:"
    echo "App Path: $APP_PATH"
    echo "Bundle ID: com.prakashjoshipax.VoiceInk"
    
    if [[ -d "$APP_PATH" ]]; then
        echo "App Size: $(du -sh "$APP_PATH" | cut -f1)"
        echo "Build Date: $(stat -f "%Sm" "$APP_PATH")"
        
        # Check frameworks
        echo ""
        log_info "Linked Frameworks:"
        if [[ -d "$APP_PATH/Contents/Frameworks" ]]; then
            ls -la "$APP_PATH/Contents/Frameworks/" | grep -E "\.(framework|dylib)$" || echo "No frameworks found"
        fi
        
        # Check whisper framework specifically
        if [[ -d "$APP_PATH/Contents/Frameworks/whisper.framework" ]]; then
            log_success "Whisper framework is present"
            echo "Whisper framework size: $(du -sh "$APP_PATH/Contents/Frameworks/whisper.framework" | cut -f1)"
        else
            log_warning "Whisper framework not found"
        fi
    else
        log_error "App not found"
    fi
}

# Check permissions
check_permissions() {
    log_info "Checking macOS permissions for VoiceInk..."
    
    # Check if we can query permissions (requires newer macOS)
    if command -v tccutil > /dev/null 2>&1; then
        log_info "Microphone permission status:"
        tccutil list Microphone 2>/dev/null | grep -i voiceink || log_warning "No microphone permission found"
        
        log_info "Accessibility permission status:"
        tccutil list Accessibility 2>/dev/null | grep -i voiceink || log_warning "No accessibility permission found"
    else
        log_info "Please check permissions manually in System Preferences > Security & Privacy"
    fi
    
    log_info "To grant permissions:"
    echo "1. Go to System Preferences > Security & Privacy > Privacy"
    echo "2. Select 'Microphone' and check VoiceInk"
    echo "3. Select 'Accessibility' and check VoiceInk"
    echo "4. You may need to restart VoiceInk after granting permissions"
}

# Open app folder
open_app_folder() {
    if [[ -d "$APP_PATH" ]]; then
        open "$(dirname "$APP_PATH")"
        log_success "Opened app folder in Finder"
    else
        log_error "App folder not found"
    fi
}

# Show logs
show_logs() {
    log_info "VoiceInk Console Logs (last 50 lines):"
    log show --predicate 'process == "VoiceInk"' --last 1h --style compact | tail -50 || log_warning "No recent logs found"
}

# Rebuild app
rebuild_app() {
    log_info "Rebuilding VoiceInk..."
    cd "$(dirname "$0")"
    
    # Stop app first
    stop_app
    
    # Build using our setup script
    if [[ -f "setup_tests.sh" ]]; then
        log_info "Using setup script to rebuild..."
        xcodebuild build -project VoiceInk.xcodeproj -scheme VoiceInk -destination 'platform=macOS' CODE_SIGN_IDENTITY="" CODE_SIGNING_REQUIRED=NO CODE_SIGNING_ALLOWED=NO
        
        if [[ $? -eq 0 ]]; then
            log_success "Rebuild completed successfully"
            start_app
        else
            log_error "Rebuild failed"
            return 1
        fi
    else
        log_error "setup_tests.sh not found. Please run from the VoiceInk project directory."
        return 1
    fi
}

# Show help
show_help() {
    echo "VoiceInk Helper Script"
    echo "====================="
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  status      - Check if VoiceInk is running"
    echo "  start       - Start VoiceInk application"
    echo "  stop        - Stop VoiceInk application"
    echo "  restart     - Restart VoiceInk application"
    echo "  info        - Show application information"
    echo "  permissions - Check and guide for permissions setup"
    echo "  folder      - Open app folder in Finder"
    echo "  logs        - Show recent application logs"
    echo "  rebuild     - Rebuild the application"
    echo "  help        - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start           # Start VoiceInk"
    echo "  $0 status          # Check if running"
    echo "  $0 permissions     # Check permissions"
}

# Main command handling
case "${1:-help}" in
    "status")
        check_status
        ;;
    "start")
        start_app
        ;;
    "stop")
        stop_app
        ;;
    "restart")
        restart_app
        ;;
    "info")
        show_info
        ;;
    "permissions")
        check_permissions
        ;;
    "folder")
        open_app_folder
        ;;
    "logs")
        show_logs
        ;;
    "rebuild")
        rebuild_app
        ;;
    "help"|*)
        show_help
        ;;
esac
