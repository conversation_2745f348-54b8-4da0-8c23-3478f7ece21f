import SwiftUI
import ScreenCaptureKit

struct MeetingTranscriptionView: View {
    @StateObject private var audioCapture = SystemAudioCaptureService.shared
    @EnvironmentObject private var whisperState: WhisperState
    
    @State private var currentTranscript = ""
    @State private var meetingSegments: [MeetingTranscript.TranscriptSegment] = []
    @State private var isRecording = false
    @State private var showingPermissionAlert = false
    @State private var activeMeetingPlatform: String?
    @State private var meetingStartTime: Date?
    
    var body: some View {
        VStack(spacing: 20) {
            // Header
            headerSection
            
            // Meeting Detection
            meetingDetectionSection
            
            // Live Transcription Display
            if isRecording {
                liveTranscriptionSection
            }
            
            // Controls
            controlsSection
            
            // Meeting History
            if !meetingSegments.isEmpty {
                meetingHistorySection
            }
            
            Spacer()
        }
        .padding()
        .onAppear {
            checkForActiveMeeting()
            Task {
                await audioCapture.refreshAvailableApplications()
            }
        }
        .alert("Permissions Required", isPresented: $showingPermissionAlert) {
            But<PERSON>("Open System Preferences") {
                openSystemPreferences()
            }
            But<PERSON>("Cancel", role: .cancel) { }
        } message: {
            Text("Jubu-Transcribe needs Screen Recording permission to capture meeting audio. Please enable it in System Preferences > Privacy & Security > Screen Recording.")
        }
    }
    
    private var headerSection: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: "video.fill")
                    .font(.title2)
                    .foregroundColor(.blue)
                
                Text("Meeting Transcription")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Spacer()
                
                if let platform = activeMeetingPlatform {
                    HStack {
                        Circle()
                            .fill(Color.green)
                            .frame(width: 8, height: 8)
                        Text(platform)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            Text("Real-time transcription for video conferencing platforms")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
    }
    
    private var meetingDetectionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Available Meeting Applications")
                .font(.headline)
            
            if audioCapture.availableApplications.isEmpty {
                VStack(spacing: 8) {
                    Image(systemName: "video.slash")
                        .font(.title)
                        .foregroundColor(.secondary)
                    
                    Text("No meeting applications detected")
                        .foregroundColor(.secondary)
                    
                    Text("Open Zoom, Teams, Google Meet, or other meeting apps to see them here")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color(.controlBackgroundColor))
                .cornerRadius(8)
            } else {
                LazyVGrid(columns: [
                    GridItem(.adaptive(minimum: 200))
                ], spacing: 12) {
                    ForEach(audioCapture.availableApplications, id: \.processID) { app in
                        meetingAppCard(app)
                    }
                }
            }
            
            Button("Refresh Applications") {
                Task {
                    await audioCapture.refreshAvailableApplications()
                    checkForActiveMeeting()
                }
            }
            .buttonStyle(.bordered)
        }
    }
    
    private func meetingAppCard(_ app: SCRunningApplication) -> some View {
        VStack(spacing: 8) {
            HStack {
                // App icon placeholder
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.blue.opacity(0.2))
                    .frame(width: 32, height: 32)
                    .overlay(
                        Image(systemName: "video.fill")
                            .foregroundColor(.blue)
                    )
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(app.applicationName)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .lineLimit(1)
                    
                    if let bundleId = app.bundleIdentifier {
                        Text(bundleId)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                    }
                }
                
                Spacer()
            }
            
            if audioCapture.selectedApplication?.processID == app.processID && isRecording {
                HStack {
                    Circle()
                        .fill(Color.red)
                        .frame(width: 6, height: 6)
                    Text("Recording")
                        .font(.caption)
                        .foregroundColor(.red)
                }
            } else {
                Button("Select") {
                    selectApplication(app)
                }
                .buttonStyle(.borderedProminent)
                .controlSize(.small)
            }
        }
        .padding()
        .background(Color(.windowBackgroundColor))
        .cornerRadius(8)
        .shadow(radius: 1)
    }
    
    private var liveTranscriptionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Live Transcription")
                    .font(.headline)
                
                Spacer()
                
                if let startTime = meetingStartTime {
                    Text("Duration: \(formatDuration(Date().timeIntervalSince(startTime)))")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            ScrollView {
                VStack(alignment: .leading, spacing: 8) {
                    if currentTranscript.isEmpty {
                        Text("Listening for audio...")
                            .foregroundColor(.secondary)
                            .italic()
                    } else {
                        Text(currentTranscript)
                            .textSelection(.enabled)
                    }
                    
                    // Show recent segments
                    ForEach(meetingSegments.suffix(5), id: \.id) { segment in
                        HStack(alignment: .top, spacing: 8) {
                            Text(formatTime(segment.timestamp))
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .frame(width: 60, alignment: .leading)
                            
                            VStack(alignment: .leading, spacing: 2) {
                                if let speaker = segment.speaker {
                                    Text(speaker)
                                        .font(.caption)
                                        .fontWeight(.semibold)
                                        .foregroundColor(.blue)
                                }
                                
                                Text(segment.text)
                                    .textSelection(.enabled)
                            }
                            
                            Spacer()
                        }
                        .padding(.vertical, 4)
                        .background(Color(.controlBackgroundColor).opacity(0.5))
                        .cornerRadius(4)
                    }
                }
                .padding()
            }
            .frame(height: 200)
            .background(Color(.textBackgroundColor))
            .cornerRadius(8)
            .border(Color(.separatorColor), width: 1)
        }
    }
    
    private var controlsSection: some View {
        HStack(spacing: 16) {
            Button(action: toggleRecording) {
                HStack {
                    Image(systemName: isRecording ? "stop.circle.fill" : "record.circle")
                        .font(.title2)
                    Text(isRecording ? "Stop Recording" : "Start Recording")
                }
            }
            .buttonStyle(.borderedProminent)
            .disabled(audioCapture.selectedApplication == nil)
            
            if isRecording {
                Button("Save Transcript") {
                    saveCurrentTranscript()
                }
                .buttonStyle(.bordered)
            }
            
            Button("Export") {
                exportTranscript()
            }
            .buttonStyle(.bordered)
            .disabled(meetingSegments.isEmpty)
        }
    }
    
    private var meetingHistorySection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Meeting Transcript")
                .font(.headline)
            
            ScrollView {
                LazyVStack(alignment: .leading, spacing: 4) {
                    ForEach(meetingSegments, id: \.id) { segment in
                        HStack(alignment: .top, spacing: 8) {
                            Text(formatTime(segment.timestamp))
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .frame(width: 60, alignment: .leading)
                            
                            VStack(alignment: .leading, spacing: 2) {
                                if let speaker = segment.speaker {
                                    Text(speaker)
                                        .font(.caption)
                                        .fontWeight(.semibold)
                                        .foregroundColor(.blue)
                                }
                                
                                Text(segment.text)
                                    .textSelection(.enabled)
                            }
                            
                            Spacer()
                        }
                        .padding(.vertical, 2)
                    }
                }
                .padding()
            }
            .frame(height: 150)
            .background(Color(.textBackgroundColor))
            .cornerRadius(8)
            .border(Color(.separatorColor), width: 1)
        }
    }
    
    // MARK: - Actions
    
    private func checkForActiveMeeting() {
        activeMeetingPlatform = audioCapture.detectActiveMeetingPlatform()
    }
    
    private func selectApplication(_ app: SCRunningApplication) {
        Task {
            let hasPermission = await audioCapture.requestPermissions()
            if hasPermission {
                audioCapture.selectedApplication = app
            } else {
                showingPermissionAlert = true
            }
        }
    }
    
    private func toggleRecording() {
        if isRecording {
            stopRecording()
        } else {
            startRecording()
        }
    }
    
    private func startRecording() {
        guard let app = audioCapture.selectedApplication else { return }
        
        Task {
            let success = await audioCapture.startCapturing(application: app)
            if success {
                isRecording = true
                meetingStartTime = Date()
                currentTranscript = ""
                
                // Start whisper transcription
                // This would integrate with existing WhisperState
            }
        }
    }
    
    private func stopRecording() {
        Task {
            await audioCapture.stopCapturing()
            isRecording = false
            meetingStartTime = nil
        }
    }
    
    private func saveCurrentTranscript() {
        if !currentTranscript.isEmpty {
            let segment = MeetingTranscript.TranscriptSegment(
                timestamp: Date(),
                speaker: nil, // Speaker detection would be implemented here
                text: currentTranscript,
                confidence: 1.0
            )
            meetingSegments.append(segment)
            currentTranscript = ""
        }
    }
    
    private func exportTranscript() {
        let transcript = meetingSegments.map { segment in
            let time = formatTime(segment.timestamp)
            if let speaker = segment.speaker {
                return "[\(time)] \(speaker): \(segment.text)"
            } else {
                return "[\(time)] \(segment.text)"
            }
        }.joined(separator: "\n")
        
        let panel = NSSavePanel()
        panel.allowedContentTypes = [.plainText]
        panel.nameFieldStringValue = "meeting-transcript-\(formatDateForFilename(Date())).txt"
        
        if panel.runModal() == .OK, let url = panel.url {
            do {
                try transcript.write(to: url, atomically: true, encoding: .utf8)
            } catch {
                print("Failed to save transcript: \(error)")
            }
        }
    }
    
    private func openSystemPreferences() {
        NSWorkspace.shared.open(URL(string: "x-apple.systempreferences:com.apple.preference.security?Privacy_ScreenCapture")!)
    }
    
    // MARK: - Helpers
    
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .medium
        return formatter.string(from: date)
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
    
    private func formatDateForFilename(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd-HHmm"
        return formatter.string(from: date)
    }
}
