import SwiftUI

struct LicenseView: View {
    @StateObject private var licenseViewModel = LicenseViewModel()
    
    var body: some View {
        VStack(spacing: 15) {
            Text("About Jubu-Transcribe")
                .font(.headline)

            VStack(spacing: 10) {
                Text("Free & Open Source")
                    .foregroundColor(.green)
                    .font(.title2)

                Text("Jubu-Transcribe is completely free to use with no limitations. Enjoy unlimited transcription on all your devices!")
                    .multilineTextAlignment(.center)
                    .foregroundColor(.secondary)
            }
            
            if let message = licenseViewModel.validationMessage {
                Text(message)
                    .foregroundColor(licenseViewModel.licenseState == .licensed ? .green : .red)
                    .font(.caption)
            }
        }
        .padding()
    }
}

struct LicenseView_Previews: PreviewProvider {
    static var previews: some View {
        LicenseView()
    }
} 