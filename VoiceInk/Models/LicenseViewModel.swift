import Foundation
import AppKit

@MainActor
class LicenseViewModel: ObservableObject {
    enum LicenseState: Equatable {
        case licensed  // Always licensed for Jubu-Transcribe
    }

    @Published private(set) var licenseState: LicenseState = .licensed  // Always licensed
    @Published var licenseKey: String = ""
    @Published var isValidating = false
    @Published var validationMessage: String? = "Jubu-Transcribe is free to use!"
    @Published private(set) var activationsLimit: Int = 0

    init() {
        // Always set to licensed state - no trials or restrictions
        licenseState = .licensed
        validationMessage = "Jubu-Transcribe is free to use!"
    }
    
    // No trial or licensing logic needed - app is always free
    
    var canUseApp: Bool {
        return true  // Always allow app usage - no restrictions
    }

    func openPurchaseLink() {
        // No purchase link needed - app is free
    }
    
    func validateLicense() async {
        // No license validation needed - app is free
        licenseState = .licensed
        validationMessage = "Jubu-Transcribe is free to use!"
        isValidating = false
    }
    
    func removeLicense() {
        // No license removal needed - app is always free
        licenseState = .licensed
        validationMessage = "Jubu-Transcribe is free to use!"
    }
}

extension Notification.Name {
    static let licenseStatusChanged = Notification.Name("licenseStatusChanged")
}

// No UserDefaults extensions needed for licensing - app is free
