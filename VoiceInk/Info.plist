<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>SUEnableInstallerLauncherService</key>
	<true/>
	<key>SUFeedURL</key>
	<string>https://beingpax.github.io/VoiceInk/appcast.xml</string>
	<key>SUPublicEDKey</key>
	<string>rLRdZIjK3gHKfqNlAF9nT7FbjwSvwkJ8BVn0v2mD1Mo=</string>
	<key>LSUIElement</key>
	<false/>
	<key>SUEnableAutomaticChecks</key>
	<true/>
	<key>NSMicrophoneUsageDescription</key>
	<string>Jubu-Transcribe needs access to your microphone to record audio for transcription.</string>
	<key>NSAppleEventsUsageDescription</key>
	<string>Jubu-Transcribe needs to interact with your browser to detect the current website for applying website-specific configurations.</string>
	<key>NSScreenCaptureUsageDescription</key>
	<string>Jubu-Transcribe needs screen recording access to understand context from your screen for improved transcription accuracy.</string>
</dict>
</plist>
