#!/bin/bash

# Create a stub whisper.xcframework for testing purposes
# This allows the VoiceInk project to build and run tests without the full whisper.cpp build

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Create the framework structure
create_stub_framework() {
    local framework_path="$1"
    
    log_info "Creating stub whisper.xcframework at $framework_path"
    
    # Create directory structure
    mkdir -p "$framework_path/macos-arm64_x86_64/whisper.framework/Versions/A/Headers"
    mkdir -p "$framework_path/macos-arm64_x86_64/whisper.framework/Versions/A/Modules"
    mkdir -p "$framework_path/macos-arm64_x86_64/whisper.framework/Versions/A/Resources"
    
    # Create symbolic links
    cd "$framework_path/macos-arm64_x86_64/whisper.framework"
    ln -sf A Versions/Current
    ln -sf Versions/Current/Headers Headers
    ln -sf Versions/Current/Modules Modules
    ln -sf Versions/Current/Resources Resources
    ln -sf Versions/Current/whisper whisper
    cd - > /dev/null
    
    # Create stub headers
    log_info "Creating stub headers..."
    
    # whisper.h stub
    cat > "$framework_path/macos-arm64_x86_64/whisper.framework/Versions/A/Headers/whisper.h" << 'EOF'
#ifndef WHISPER_H
#define WHISPER_H

#ifdef __cplusplus
extern "C" {
#endif

// Stub whisper.h for testing purposes
// This is a minimal implementation to allow compilation

typedef struct whisper_context whisper_context;
typedef struct whisper_state whisper_state;

struct whisper_context_params {
    bool use_gpu;
};

struct whisper_full_params {
    int strategy;
    int n_threads;
    int n_max_text_ctx;
    int offset_ms;
    int duration_ms;
    bool translate;
    bool no_context;
    bool single_segment;
    bool print_special;
    bool print_progress;
    bool print_realtime;
    bool print_timestamps;
    const char * language;
    const char * prompt;
    float temperature;
    float max_initial_ts;
    float length_penalty;
    float temperature_inc;
    float entropy_thold;
    float logprob_thold;
    float no_speech_thold;
};

// Stub function declarations
whisper_context * whisper_init_from_file(const char * path_model);
whisper_context * whisper_init_from_buffer(void * buffer, size_t buffer_size);
void whisper_free(struct whisper_context * ctx);

struct whisper_context_params whisper_context_default_params(void);
struct whisper_full_params whisper_full_default_params(int strategy);

int whisper_full(
    struct whisper_context * ctx,
    struct whisper_full_params params,
    const float * samples,
    int n_samples);

int whisper_full_n_segments(struct whisper_context * ctx);
const char * whisper_full_get_segment_text(struct whisper_context * ctx, int i_segment);

#ifdef __cplusplus
}
#endif

#endif
EOF

    # Create other stub headers
    for header in ggml.h ggml-alloc.h ggml-backend.h ggml-metal.h ggml-cpu.h ggml-blas.h gguf.h; do
        cat > "$framework_path/macos-arm64_x86_64/whisper.framework/Versions/A/Headers/$header" << EOF
#ifndef ${header^^}_H
#define ${header^^}_H

// Stub $header for testing purposes

#ifdef __cplusplus
extern "C" {
#endif

// Minimal stub implementation

#ifdef __cplusplus
}
#endif

#endif
EOF
    done
    
    # Create module map
    log_info "Creating module map..."
    cat > "$framework_path/macos-arm64_x86_64/whisper.framework/Versions/A/Modules/module.modulemap" << 'EOF'
framework module whisper {
    header "whisper.h"
    header "ggml.h"
    header "ggml-alloc.h"
    header "ggml-backend.h"
    header "ggml-metal.h"
    header "ggml-cpu.h"
    header "ggml-blas.h"
    header "gguf.h"

    link "c++"
    link framework "Accelerate"
    link framework "Metal"
    link framework "Foundation"
    link framework "CoreML"

    export *
}
EOF

    # Create Info.plist for framework
    log_info "Creating framework Info.plist..."
    cat > "$framework_path/macos-arm64_x86_64/whisper.framework/Versions/A/Resources/Info.plist" << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleDevelopmentRegion</key>
    <string>en</string>
    <key>CFBundleExecutable</key>
    <string>whisper</string>
    <key>CFBundleIdentifier</key>
    <string>org.ggml.whisper.stub</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundleName</key>
    <string>whisper</string>
    <key>CFBundlePackageType</key>
    <string>FMWK</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0.0-stub</string>
    <key>CFBundleVersion</key>
    <string>1</string>
    <key>MinimumOSVersion</key>
    <string>13.3</string>
    <key>CFBundleSupportedPlatforms</key>
    <array>
        <string>MacOSX</string>
    </array>
    <key>DTPlatformName</key>
    <string>macosx</string>
    <key>DTSDKName</key>
    <string>macosx13.3</string>
</dict>
</plist>
EOF

    # Create stub dynamic library
    log_info "Creating stub dynamic library..."
    cat > /tmp/whisper_stub.c << 'EOF'
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// Stub implementations for whisper functions
typedef struct whisper_context whisper_context;

struct whisper_context_params {
    int use_gpu;
};

struct whisper_full_params {
    int strategy;
    int n_threads;
    int n_max_text_ctx;
    int offset_ms;
    int duration_ms;
    int translate;
    int no_context;
    int single_segment;
    int print_special;
    int print_progress;
    int print_realtime;
    int print_timestamps;
    const char * language;
    const char * prompt;
    float temperature;
    float max_initial_ts;
    float length_penalty;
    float temperature_inc;
    float entropy_thold;
    float logprob_thold;
    float no_speech_thold;
};

whisper_context * whisper_init_from_file(const char * path_model) {
    printf("STUB: whisper_init_from_file called with %s\n", path_model);
    return (whisper_context*)malloc(sizeof(int)); // Dummy pointer
}

whisper_context * whisper_init_from_buffer(void * buffer, size_t buffer_size) {
    printf("STUB: whisper_init_from_buffer called\n");
    return (whisper_context*)malloc(sizeof(int)); // Dummy pointer
}

void whisper_free(struct whisper_context * ctx) {
    printf("STUB: whisper_free called\n");
    if (ctx) free(ctx);
}

struct whisper_context_params whisper_context_default_params(void) {
    struct whisper_context_params params = {0};
    return params;
}

struct whisper_full_params whisper_full_default_params(int strategy) {
    struct whisper_full_params params = {0};
    params.strategy = strategy;
    return params;
}

int whisper_full(struct whisper_context * ctx, struct whisper_full_params params, const float * samples, int n_samples) {
    printf("STUB: whisper_full called with %d samples\n", n_samples);
    return 0; // Success
}

int whisper_full_n_segments(struct whisper_context * ctx) {
    printf("STUB: whisper_full_n_segments called\n");
    return 1; // Return 1 segment
}

const char * whisper_full_get_segment_text(struct whisper_context * ctx, int i_segment) {
    printf("STUB: whisper_full_get_segment_text called for segment %d\n", i_segment);
    return "This is a stub transcription result.";
}
EOF

    # Compile the stub library
    clang -dynamiclib \
        -arch arm64 -arch x86_64 \
        -mmacosx-version-min=13.3 \
        -install_name "@rpath/whisper.framework/Versions/Current/whisper" \
        -o "$framework_path/macos-arm64_x86_64/whisper.framework/Versions/A/whisper" \
        /tmp/whisper_stub.c
    
    # Create XCFramework Info.plist
    log_info "Creating XCFramework Info.plist..."
    cat > "$framework_path/Info.plist" << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>AvailableLibraries</key>
    <array>
        <dict>
            <key>LibraryIdentifier</key>
            <string>macos-arm64_x86_64</string>
            <key>LibraryPath</key>
            <string>whisper.framework</string>
            <key>SupportedArchitectures</key>
            <array>
                <string>arm64</string>
                <string>x86_64</string>
            </array>
            <key>SupportedPlatform</key>
            <string>macos</string>
        </dict>
    </array>
    <key>CFBundlePackageType</key>
    <string>XFWK</string>
    <key>XCFrameworkFormatVersion</key>
    <string>1.0</string>
</dict>
</plist>
EOF

    # Clean up
    rm -f /tmp/whisper_stub.c
    
    log_success "Stub whisper.xcframework created successfully!"
    log_warning "This is a STUB framework for testing only. It does not provide real speech recognition."
}

# Main execution
main() {
    log_info "Creating stub whisper.xcframework for VoiceInk testing..."
    
    # Create both possible locations
    mkdir -p ../build-apple
    create_stub_framework "../build-apple/whisper.xcframework"
    
    mkdir -p ../Downloads/build-apple
    create_stub_framework "../Downloads/build-apple/whisper.xcframework"
    
    log_success "Stub frameworks created at both expected locations:"
    log_info "  - ../build-apple/whisper.xcframework"
    log_info "  - ../Downloads/build-apple/whisper.xcframework"
    log_warning ""
    log_warning "IMPORTANT: These are STUB frameworks for testing only!"
    log_warning "They allow the project to build and run tests but do not provide real speech recognition."
    log_warning "For production use, you need to build the real whisper.xcframework from whisper.cpp."
}

main "$@"
