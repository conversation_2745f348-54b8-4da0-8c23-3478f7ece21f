# Contributing to VoiceInk

First off, thank you for considering contributing to VoiceInk! It's people like you that make VoiceInk such a great tool.

## Important Notice About Pull Requests

We welcome forks and improvements to VoiceInk! However, please note:

1. **The acceptance of Pull Requests is solely at the discretion of the project maintainers**
2. **Before making significant changes or starting work on major features:**
   - Open an issue to discuss your proposed changes
   - Wait for maintainer feedback and approval
   - This helps ensure your time and effort align with the project's direction
3. **For fundamental or architectural changes:**
   - Direct consultation with the maintainers is required
   - These discussions should happen before you start the implementation

This policy helps ensure:
- Your valuable time is spent on changes that align with the project's vision
- The codebase maintains its consistency and quality
- We can provide proper guidance and support for your contribution

## Important Notice

Before starting work on any new feature or fix, please reach out to us first by opening an issue or discussion. This is crucial because:

1. We want to ensure your contribution aligns with the project's goals and vision
2. Someone else might already be working on something similar
3. We might have valuable insights or requirements that could save you time
4. Your proposed changes might need some adjustments to fit with our roadmap

## Code of Conduct

By participating in this project, you agree to abide by our [Code of Conduct](CODE_OF_CONDUCT.md).

## How Can I Contribute?

### Reporting Bugs

- Before submitting a bug report, please check if the issue has already been reported
- Use the bug report template when creating an issue
- Include as much relevant information as possible
- Include steps to reproduce the issue

### Suggesting Enhancements

- Open an issue using the feature request template
- Clearly describe the feature and its benefits
- Discuss potential implementation approaches
- Consider the feature's impact on existing functionality

### Pull Requests

1. Fork the repository
2. Create a new branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Run tests and ensure they pass
5. Commit your changes (`git commit -m 'Add some amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

### Development Process

1. Ensure you have all the requirements installed:
   - macOS 14.0 or later
   - Latest version of Xcode
   - Latest version of Swift
   - whisper.cpp properly set up

2. Follow our coding standards:
   - Use Swift style guidelines
   - Write meaningful commit messages
   - Include comments where necessary
   - Add tests for new features

3. Testing:
   - Run existing tests
   - Add new tests for new functionality
   - Ensure all tests pass before submitting PR

## Style Guidelines

- Follow Swift style guidelines
- Use meaningful variable and function names
- Keep functions focused and concise
- Comment complex logic
- Write self-documenting code where possible

## Community

- Join our discussions
- Help other contributors
- Share your ideas
- Be respectful and constructive

## Questions?

If you have any questions or need clarification, feel free to:
1. Open an issue
2. Start a discussion
3. Reach out to the maintainers

Thank you for contributing to VoiceInk! 🎉 