#!/bin/bash

# Simplified whisper.cpp build script for macOS only
# This script builds only the macOS version of whisper.xcframework

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
MACOS_MIN_OS_VERSION=13.3
BUILD_SHARED_LIBS=OFF
WHISPER_BUILD_EXAMPLES=OFF
WHISPER_BUILD_TESTS=OFF
WHISPER_BUILD_SERVER=OFF
GGML_METAL=ON
GGML_METAL_EMBED_LIBRARY=ON
GGML_BLAS_DEFAULT=ON
GGML_METAL_USE_BF16=ON
GGML_OPENMP=OFF

COMMON_C_FLAGS="-Wno-macro-redefined -Wno-shorten-64-to-32 -Wno-unused-command-line-argument -g"
COMMON_CXX_FLAGS="-Wno-macro-redefined -Wno-shorten-64-to-32 -Wno-unused-command-line-argument -g"

# Common options for macOS build
COMMON_CMAKE_ARGS=(
    -DCMAKE_XCODE_ATTRIBUTE_CODE_SIGNING_REQUIRED=NO
    -DCMAKE_XCODE_ATTRIBUTE_CODE_SIGN_IDENTITY=""
    -DCMAKE_XCODE_ATTRIBUTE_CODE_SIGNING_ALLOWED=NO
    -DCMAKE_XCODE_ATTRIBUTE_DEBUG_INFORMATION_FORMAT="dwarf-with-dsym"
    -DCMAKE_XCODE_ATTRIBUTE_GCC_GENERATE_DEBUGGING_SYMBOLS=YES
    -DCMAKE_XCODE_ATTRIBUTE_COPY_PHASE_STRIP=NO
    -DCMAKE_XCODE_ATTRIBUTE_STRIP_INSTALLED_PRODUCT=NO
    -DCMAKE_XCODE_ATTRIBUTE_DEVELOPMENT_TEAM=ggml
    -DBUILD_SHARED_LIBS=${BUILD_SHARED_LIBS}
    -DWHISPER_BUILD_EXAMPLES=${WHISPER_BUILD_EXAMPLES}
    -DWHISPER_BUILD_TESTS=${WHISPER_BUILD_TESTS}
    -DWHISPER_BUILD_SERVER=${WHISPER_BUILD_SERVER}
    -DGGML_METAL_EMBED_LIBRARY=${GGML_METAL_EMBED_LIBRARY}
    -DGGML_BLAS_DEFAULT=${GGML_BLAS_DEFAULT}
    -DGGML_METAL=${GGML_METAL}
    -DGGML_METAL_USE_BF16=${GGML_METAL_USE_BF16}
    -DGGML_NATIVE=OFF
    -DGGML_OPENMP=${GGML_OPENMP}
)

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check required tools
check_required_tool() {
    local tool=$1
    local install_message=$2

    if ! command_exists $tool; then
        log_error "$tool is required but not found."
        echo "$install_message"
        exit 1
    fi
}

log_info "Checking for required tools..."
check_required_tool "cmake" "Please install CMake 3.28.0 or later (brew install cmake)"

# Clean up previous builds
log_info "Cleaning up previous builds..."
rm -rf build-apple
rm -rf build-macos

# Build for macOS only
log_info "Building whisper.cpp for macOS..."
cmake -B build-macos \
    "${COMMON_CMAKE_ARGS[@]}" \
    -DCMAKE_OSX_DEPLOYMENT_TARGET=${MACOS_MIN_OS_VERSION} \
    -DCMAKE_OSX_ARCHITECTURES="arm64;x86_64" \
    -DCMAKE_C_FLAGS="${COMMON_C_FLAGS}" \
    -DCMAKE_CXX_FLAGS="${COMMON_CXX_FLAGS}" \
    -DWHISPER_COREML="ON" \
    -DWHISPER_COREML_ALLOW_FALLBACK="ON" \
    -S .

if [ $? -ne 0 ]; then
    log_error "Failed to configure cmake for macOS build"
    exit 1
fi

log_info "Compiling whisper.cpp (this may take several minutes)..."
cmake --build build-macos --config Release

if [ $? -ne 0 ]; then
    log_error "Failed to build whisper.cpp for macOS"
    exit 1
fi

# Create framework structure
log_info "Creating framework structure..."
mkdir -p build-apple
mkdir -p build-apple/whisper.xcframework/macos-arm64_x86_64

# Create the framework directory structure
framework_dir="build-apple/whisper.xcframework/macos-arm64_x86_64/whisper.framework"
mkdir -p "${framework_dir}/Versions/A/Headers"
mkdir -p "${framework_dir}/Versions/A/Modules"
mkdir -p "${framework_dir}/Versions/A/Resources"

# Create symbolic links
ln -sf A "${framework_dir}/Versions/Current"
ln -sf Versions/Current/Headers "${framework_dir}/Headers"
ln -sf Versions/Current/Modules "${framework_dir}/Modules"
ln -sf Versions/Current/Resources "${framework_dir}/Resources"
ln -sf Versions/Current/whisper "${framework_dir}/whisper"

# Copy headers
log_info "Copying headers..."
cp include/whisper.h "${framework_dir}/Versions/A/Headers/"
cp ggml/include/ggml.h "${framework_dir}/Versions/A/Headers/"
cp ggml/include/ggml-alloc.h "${framework_dir}/Versions/A/Headers/"
cp ggml/include/ggml-backend.h "${framework_dir}/Versions/A/Headers/"
cp ggml/include/ggml-metal.h "${framework_dir}/Versions/A/Headers/"
cp ggml/include/ggml-cpu.h "${framework_dir}/Versions/A/Headers/"
cp ggml/include/ggml-blas.h "${framework_dir}/Versions/A/Headers/"
cp ggml/include/gguf.h "${framework_dir}/Versions/A/Headers/"

# Create module map
log_info "Creating module map..."
cat > "${framework_dir}/Versions/A/Modules/module.modulemap" << EOF
framework module whisper {
    header "whisper.h"
    header "ggml.h"
    header "ggml-alloc.h"
    header "ggml-backend.h"
    header "ggml-metal.h"
    header "ggml-cpu.h"
    header "ggml-blas.h"
    header "gguf.h"

    link "c++"
    link framework "Accelerate"
    link framework "Metal"
    link framework "Foundation"
    link framework "CoreML"

    export *
}
EOF

# Create Info.plist
log_info "Creating Info.plist..."
cat > "${framework_dir}/Versions/A/Resources/Info.plist" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleDevelopmentRegion</key>
    <string>en</string>
    <key>CFBundleExecutable</key>
    <string>whisper</string>
    <key>CFBundleIdentifier</key>
    <string>org.ggml.whisper</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundleName</key>
    <string>whisper</string>
    <key>CFBundlePackageType</key>
    <string>FMWK</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0</string>
    <key>CFBundleVersion</key>
    <string>1</string>
    <key>MinimumOSVersion</key>
    <string>${MACOS_MIN_OS_VERSION}</string>
    <key>CFBundleSupportedPlatforms</key>
    <array>
        <string>MacOSX</string>
    </array>
    <key>DTPlatformName</key>
    <string>macosx</string>
    <key>DTSDKName</key>
    <string>macosx${MACOS_MIN_OS_VERSION}</string>
</dict>
</plist>
EOF

# Combine static libraries into dynamic library
log_info "Creating dynamic library..."
temp_dir="build-macos/temp"
mkdir -p "${temp_dir}"

# Check which libraries actually exist
libs=()
potential_libs=(
    "build-macos/src/libwhisper.a"
    "build-macos/ggml/src/libggml.a"
    "build-macos/ggml/src/libggml-base.a"
    "build-macos/ggml/src/libggml-cpu.a"
    "build-macos/ggml/src/ggml-metal/libggml-metal.a"
    "build-macos/ggml/src/ggml-blas/libggml-blas.a"
    "build-macos/src/libwhisper.coreml.a"
)

for lib in "${potential_libs[@]}"; do
    if [[ -f "$lib" ]]; then
        libs+=("$lib")
        log_info "Found library: $lib"
    else
        log_info "Library not found: $lib"
    fi
done

if [[ ${#libs[@]} -eq 0 ]]; then
    log_error "No static libraries found to combine"
    exit 1
fi

# Combine static libraries
log_info "Combining ${#libs[@]} static libraries..."
libtool -static -o "${temp_dir}/combined.a" "${libs[@]}" 2> /dev/null

if [[ $? -ne 0 ]]; then
    log_error "Failed to combine static libraries"
    exit 1
fi

# Create dynamic library
log_info "Creating dynamic library with clang++..."
clang++ -dynamiclib \
    -arch arm64 -arch x86_64 \
    -mmacosx-version-min=${MACOS_MIN_OS_VERSION} \
    -Wl,-force_load,"${temp_dir}/combined.a" \
    -framework Foundation -framework Metal -framework Accelerate -framework CoreML \
    -install_name "@rpath/whisper.framework/Versions/Current/whisper" \
    -o "${framework_dir}/Versions/A/whisper"

if [[ $? -ne 0 ]]; then
    log_error "Failed to create dynamic library"
    exit 1
fi

# Create XCFramework Info.plist
log_info "Creating XCFramework Info.plist..."
cat > "build-apple/whisper.xcframework/Info.plist" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>AvailableLibraries</key>
    <array>
        <dict>
            <key>LibraryIdentifier</key>
            <string>macos-arm64_x86_64</string>
            <key>LibraryPath</key>
            <string>whisper.framework</string>
            <key>SupportedArchitectures</key>
            <array>
                <string>arm64</string>
                <string>x86_64</string>
            </array>
            <key>SupportedPlatform</key>
            <string>macos</string>
        </dict>
    </array>
    <key>CFBundlePackageType</key>
    <string>XFWK</string>
    <key>XCFrameworkFormatVersion</key>
    <string>1.0</string>
</dict>
</plist>
EOF

# Clean up
rm -rf "${temp_dir}"

log_success "whisper.xcframework built successfully for macOS!"
log_info "Framework location: build-apple/whisper.xcframework"
