# VoiceInk Dependencies Guide

This guide explains how to install the required dependencies for building and testing VoiceInk.

## System Requirements

- **macOS 14.0+** (required)
- **Xcode** (full version, not just Command Line Tools)
- **cmake 3.28.0+** (for building whisper.cpp)
- **Git** (for cloning repositories)

## Installation Instructions

### 1. Install Xcode

**Option A: Mac App Store (Recommended)**
1. Open the Mac App Store
2. Search for "Xcode"
3. Click "Install" or "Get"
4. Wait for the download and installation to complete

**Option B: Apple Developer Website**
1. Visit [developer.apple.com](https://developer.apple.com/xcode/)
2. Sign in with your Apple ID
3. Download the latest Xcode version
4. Install the downloaded .xip file

**Verify Installation:**
```bash
xcodebuild -version
```

### 2. Install cmake

**Option A: Homebrew (Recommended)**
```bash
# Install Homebrew if you don't have it
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install cmake
brew install cmake
```

**Option B: Official Installer**
1. Visit [cmake.org/download](https://cmake.org/download/)
2. Download the macOS installer
3. Run the installer and follow the instructions

**Option C: MacPorts**
```bash
sudo port install cmake
```

**Verify Installation:**
```bash
cmake --version
```

### 3. Install Git (if not already installed)

Git is usually pre-installed on macOS, but you can install/update it:

**Using Homebrew:**
```bash
brew install git
```

**Using Xcode Command Line Tools:**
```bash
xcode-select --install
```

**Verify Installation:**
```bash
git --version
```

## Quick Setup Script

You can use our automated setup script to check and install most dependencies:

```bash
# Make the script executable
chmod +x setup_tests.sh

# Run setup only (doesn't run tests)
./setup_tests.sh setup
```

The script will:
- Check your macOS version
- Verify Xcode installation
- Check for cmake and provide installation instructions
- Clone and build whisper.cpp dependency
- Set up the development environment

## Manual Dependency Setup

If you prefer to set up dependencies manually:

### 1. Clone whisper.cpp
```bash
cd ..  # Go to parent directory
git clone https://github.com/ggerganov/whisper.cpp.git
cd whisper.cpp
```

### 2. Build whisper.xcframework
```bash
# Make sure cmake is installed first
cmake --version

# Build the framework (takes 5-10 minutes)
./build-xcframework.sh
```

### 3. Verify Framework
```bash
# Check if the framework was built successfully
ls -la build-apple/whisper.xcframework
```

## Troubleshooting

### Common Issues

#### 1. "cmake not found"
```bash
# Install cmake using Homebrew
brew install cmake

# Or add cmake to your PATH if installed elsewhere
export PATH="/Applications/CMake.app/Contents/bin:$PATH"
```

#### 2. "xcodebuild requires Xcode"
- Install full Xcode from Mac App Store (not just Command Line Tools)
- Run `sudo xcode-select -s /Applications/Xcode.app/Contents/Developer`

#### 3. "Permission denied" when running scripts
```bash
chmod +x setup_tests.sh
```

#### 4. Homebrew not found
```bash
# Install Homebrew
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Add to PATH (for Apple Silicon Macs)
echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zprofile
eval "$(/opt/homebrew/bin/brew shellenv)"
```

### Environment Variables

You may need to set these environment variables:

```bash
# Add to ~/.zshrc or ~/.bash_profile
export PATH="/opt/homebrew/bin:$PATH"  # For Homebrew on Apple Silicon
export PATH="/Applications/CMake.app/Contents/bin:$PATH"  # For cmake GUI install
```

## Verification Commands

Run these commands to verify all dependencies are properly installed:

```bash
# Check macOS version
sw_vers -productVersion

# Check Xcode
xcodebuild -version

# Check Swift
swift --version

# Check cmake
cmake --version

# Check Git
git --version

# Check if whisper.xcframework exists
ls -la ../whisper.cpp/build-apple/whisper.xcframework
```

## Next Steps

Once all dependencies are installed:

1. **Run the setup script**: `./setup_tests.sh setup`
2. **Build the project**: `make build` or `xcodebuild build -project VoiceInk.xcodeproj -scheme VoiceInk`
3. **Run tests**: `./setup_tests.sh test` or `make test`

For detailed testing instructions, see [TESTING.md](TESTING.md).
