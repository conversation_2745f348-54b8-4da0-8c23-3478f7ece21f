# VoiceInk Makefile
# Provides convenient commands for building and testing VoiceInk

.PHONY: help setup test test-unit test-ui clean build install-deps

# Default target
help:
	@echo "VoiceInk Build and Test Commands"
	@echo "================================"
	@echo ""
	@echo "Setup and Dependencies:"
	@echo "  setup          - Setup all dependencies (including whisper.cpp)"
	@echo "  install-deps   - Install/update Swift package dependencies"
	@echo ""
	@echo "Building:"
	@echo "  build          - Build the VoiceInk project"
	@echo "  clean          - Clean build artifacts"
	@echo ""
	@echo "Testing:"
	@echo "  test           - Run all tests (unit + UI)"
	@echo "  test-unit      - Run unit tests only"
	@echo "  test-ui        - Run UI tests only"
	@echo ""
	@echo "Utilities:"
	@echo "  check-deps     - Check system dependencies"
	@echo "  list-schemes   - List available Xcode schemes"
	@echo ""

# Setup all dependencies
setup:
	@echo "Setting up VoiceInk development environment..."
	./setup_tests.sh setup

# Install Swift package dependencies
install-deps:
	@echo "Resolving Swift package dependencies..."
	xcodebuild -resolvePackageDependencies -project VoiceInk.xcodeproj

# Build the project
build:
	@echo "Building VoiceInk..."
	xcodebuild build -project VoiceInk.xcodeproj -scheme VoiceInk -destination 'platform=macOS'

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	xcodebuild clean -project VoiceInk.xcodeproj -scheme VoiceInk

# Run all tests
test:
	@echo "Running all tests..."
	./setup_tests.sh all

# Run unit tests only
test-unit:
	@echo "Running unit tests..."
	./setup_tests.sh unit

# Run UI tests only
test-ui:
	@echo "Running UI tests..."
	./setup_tests.sh ui

# Check system dependencies
check-deps:
	@echo "Checking system dependencies..."
	@echo "macOS version: $$(sw_vers -productVersion)"
	@echo "Xcode version: $$(xcodebuild -version 2>/dev/null || echo 'Not installed')"
	@echo "Swift version: $$(swift --version | head -n1)"
	@echo "Git version: $$(git --version)"

# List available Xcode schemes
list-schemes:
	@echo "Available Xcode schemes:"
	xcodebuild -list -project VoiceInk.xcodeproj

# Quick development workflow
dev-test: clean build test-unit
	@echo "Development test cycle completed!"

# Full CI workflow
ci: setup test
	@echo "CI workflow completed!"
