//
//  VoiceInkTests.swift
//  VoiceInkTests
//
//  Created by <PERSON> on 15/10/2024.
//

import Testing
import Foundation
@testable import VoiceInk

struct VoiceInkTests {

    // MARK: - Transcription Model Tests

    @Test("Transcription model initialization")
    func transcriptionInitialization() async throws {
        // Test creating a new transcription
        let text = "Hello, this is a test transcription."
        let duration: TimeInterval = 5.0
        let enhancedText = "Hello, this is a test transcription with enhanced formatting."

        let transcription = Transcription(
            text: text,
            duration: duration,
            enhancedText: enhancedText
        )

        // Verify the transcription was created correctly
        #expect(transcription.text == text)
        #expect(transcription.duration == duration)
        #expect(transcription.enhancedText == enhancedText)
        #expect(transcription.id != UUID()) // Should have a unique ID
        #expect(transcription.timestamp <= Date()) // Should be created recently
    }

    @Test("Transcription without enhanced text")
    func transcriptionWithoutEnhancedText() async throws {
        let text = "Simple transcription"
        let duration: TimeInterval = 2.5

        let transcription = Transcription(text: text, duration: duration)

        #expect(transcription.text == text)
        #expect(transcription.duration == duration)
        #expect(transcription.enhancedText == nil)
        #expect(transcription.audioFileURL == nil)
    }

    // MARK: - Word Replacement Service Tests

    @Test("Word replacement with no replacements")
    func wordReplacementNoReplacements() async throws {
        // Clear any existing replacements
        UserDefaults.standard.removeObject(forKey: "wordReplacements")

        let service = WordReplacementService.shared
        let originalText = "This is a test sentence."
        let result = service.applyReplacements(to: originalText)

        #expect(result == originalText)
    }

    @Test("Word replacement with single replacement")
    func wordReplacementSingleReplacement() async throws {
        // Set up a test replacement
        let replacements = ["test": "example"]
        UserDefaults.standard.set(replacements, forKey: "wordReplacements")

        let service = WordReplacementService.shared
        let originalText = "This is a test sentence."
        let expectedText = "This is a example sentence."
        let result = service.applyReplacements(to: originalText)

        #expect(result == expectedText)

        // Clean up
        UserDefaults.standard.removeObject(forKey: "wordReplacements")
    }

    @Test("Word replacement case insensitive")
    func wordReplacementCaseInsensitive() async throws {
        let replacements = ["hello": "hi"]
        UserDefaults.standard.set(replacements, forKey: "wordReplacements")

        let service = WordReplacementService.shared
        let originalText = "Hello world! HELLO there. hello again."
        let expectedText = "hi world! hi there. hi again."
        let result = service.applyReplacements(to: originalText)

        #expect(result == expectedText)

        // Clean up
        UserDefaults.standard.removeObject(forKey: "wordReplacements")
    }

    @Test("Word replacement whole word only")
    func wordReplacementWholeWordOnly() async throws {
        let replacements = ["cat": "dog"]
        UserDefaults.standard.set(replacements, forKey: "wordReplacements")

        let service = WordReplacementService.shared
        let originalText = "The cat is in the category."
        let expectedText = "The dog is in the category." // "category" should not be affected
        let result = service.applyReplacements(to: originalText)

        #expect(result == expectedText)

        // Clean up
        UserDefaults.standard.removeObject(forKey: "wordReplacements")
    }

    @Test("Word replacement multiple replacements")
    func wordReplacementMultiple() async throws {
        let replacements = [
            "apple": "orange",
            "red": "blue",
            "big": "small"
        ]
        UserDefaults.standard.set(replacements, forKey: "wordReplacements")

        let service = WordReplacementService.shared
        let originalText = "The big red apple is delicious."
        let expectedText = "The small blue orange is delicious."
        let result = service.applyReplacements(to: originalText)

        #expect(result == expectedText)

        // Clean up
        UserDefaults.standard.removeObject(forKey: "wordReplacements")
    }

    // MARK: - Edge Cases and Error Handling

    @Test("Word replacement with empty text")
    func wordReplacementEmptyText() async throws {
        let replacements = ["test": "example"]
        UserDefaults.standard.set(replacements, forKey: "wordReplacements")

        let service = WordReplacementService.shared
        let result = service.applyReplacements(to: "")

        #expect(result == "")

        // Clean up
        UserDefaults.standard.removeObject(forKey: "wordReplacements")
    }

    @Test("Word replacement with special characters")
    func wordReplacementSpecialCharacters() async throws {
        let replacements = ["C++": "Swift"]
        UserDefaults.standard.set(replacements, forKey: "wordReplacements")

        let service = WordReplacementService.shared
        let originalText = "I love programming in C++!"
        let expectedText = "I love programming in Swift!"
        let result = service.applyReplacements(to: originalText)

        #expect(result == expectedText)

        // Clean up
        UserDefaults.standard.removeObject(forKey: "wordReplacements")
    }

}
