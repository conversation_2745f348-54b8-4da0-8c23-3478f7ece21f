#!/usr/bin/env swift

import Foundation

// Simple test demonstration for VoiceInk
print("🧪 VoiceInk Unit Testing Demo")
print(String(repeating: "=", count: 50))

// Mock Transcription class (simplified version of the real one)
class MockTranscription {
    var id: UUID
    var text: String
    var enhancedText: String?
    var timestamp: Date
    var duration: TimeInterval
    var audioFileURL: String?
    
    init(text: String, duration: TimeInterval, enhancedText: String? = nil, audioFileURL: String? = nil) {
        self.id = UUID()
        self.text = text
        self.enhancedText = enhancedText
        self.timestamp = Date()
        self.duration = duration
        self.audioFileURL = audioFileURL
    }
}

// Mock Word Replacement Service
class MockWordReplacementService {
    static let shared = MockWordReplacementService()
    
    private init() {}
    
    func applyReplacements(to text: String) -> String {
        guard let replacements = UserDefaults.standard.dictionary(forKey: "wordReplacements") as? [String: String],
              !replacements.isEmpty else {
            return text
        }
        
        var modifiedText = text
        
        for (original, replacement) in replacements {
            let pattern = "\\b\(NSRegularExpression.escapedPattern(for: original))\\b"
            if let regex = try? NSRegularExpression(pattern: pattern, options: .caseInsensitive) {
                let range = NSRange(modifiedText.startIndex..., in: modifiedText)
                modifiedText = regex.stringByReplacingMatches(
                    in: modifiedText,
                    options: [],
                    range: range,
                    withTemplate: replacement
                )
            }
        }
        
        return modifiedText
    }
}

// Simple test function
func runTest(_ name: String, test: () -> Bool) {
    if test() {
        print("✅ \(name)")
    } else {
        print("❌ \(name)")
    }
}

// Test 1: Transcription Model
print("\n📝 Testing Transcription Model:")
runTest("Transcription initialization") {
    let text = "Hello, this is a test transcription."
    let duration: TimeInterval = 5.0
    let enhancedText = "Hello, this is a test transcription with enhanced formatting."
    
    let transcription = MockTranscription(
        text: text,
        duration: duration,
        enhancedText: enhancedText
    )
    
    return transcription.text == text &&
           transcription.duration == duration &&
           transcription.enhancedText == enhancedText &&
           transcription.timestamp <= Date()
}

runTest("Transcription without enhanced text") {
    let text = "Simple transcription"
    let duration: TimeInterval = 2.5
    
    let transcription = MockTranscription(text: text, duration: duration)
    
    return transcription.text == text &&
           transcription.duration == duration &&
           transcription.enhancedText == nil &&
           transcription.audioFileURL == nil
}

// Test 2: Word Replacement Service
print("\n🔄 Testing Word Replacement Service:")

runTest("No replacements configured") {
    UserDefaults.standard.removeObject(forKey: "wordReplacements")
    
    let service = MockWordReplacementService.shared
    let originalText = "This is a test sentence."
    let result = service.applyReplacements(to: originalText)
    
    return result == originalText
}

runTest("Single word replacement") {
    let replacements = ["test": "example"]
    UserDefaults.standard.set(replacements, forKey: "wordReplacements")
    
    let service = MockWordReplacementService.shared
    let originalText = "This is a test sentence."
    let expectedText = "This is a example sentence."
    let result = service.applyReplacements(to: originalText)
    
    UserDefaults.standard.removeObject(forKey: "wordReplacements")
    return result == expectedText
}

runTest("Case insensitive replacement") {
    let replacements = ["hello": "hi"]
    UserDefaults.standard.set(replacements, forKey: "wordReplacements")
    
    let service = MockWordReplacementService.shared
    let originalText = "Hello world! HELLO there. hello again."
    let expectedText = "hi world! hi there. hi again."
    let result = service.applyReplacements(to: originalText)
    
    UserDefaults.standard.removeObject(forKey: "wordReplacements")
    return result == expectedText
}

runTest("Whole word replacement only") {
    let replacements = ["cat": "dog"]
    UserDefaults.standard.set(replacements, forKey: "wordReplacements")
    
    let service = MockWordReplacementService.shared
    let originalText = "The cat is in the category."
    let expectedText = "The dog is in the category." // "category" should not be affected
    let result = service.applyReplacements(to: originalText)
    
    UserDefaults.standard.removeObject(forKey: "wordReplacements")
    return result == expectedText
}

runTest("Multiple replacements") {
    let replacements = [
        "apple": "orange",
        "red": "blue",
        "big": "small"
    ]
    UserDefaults.standard.set(replacements, forKey: "wordReplacements")
    
    let service = MockWordReplacementService.shared
    let originalText = "The big red apple is delicious."
    let expectedText = "The small blue orange is delicious."
    let result = service.applyReplacements(to: originalText)
    
    UserDefaults.standard.removeObject(forKey: "wordReplacements")
    return result == expectedText
}

runTest("Empty text handling") {
    let replacements = ["test": "example"]
    UserDefaults.standard.set(replacements, forKey: "wordReplacements")
    
    let service = MockWordReplacementService.shared
    let result = service.applyReplacements(to: "")
    
    UserDefaults.standard.removeObject(forKey: "wordReplacements")
    return result == ""
}

print("\n" + String(repeating: "=", count: 50))
print("🎉 Test Demo Complete!")
print("\n📚 What you learned:")
print("• How to create mock objects for testing")
print("• Testing model initialization and properties")
print("• Testing service methods with different scenarios")
print("• Testing edge cases (empty input, no configuration)")
print("• Using UserDefaults in tests with proper cleanup")
print("• Structuring tests for readability and maintainability")

print("\n🚀 Next Steps:")
print("• Install full Xcode to run the real test suite")
print("• Add more comprehensive tests to VoiceInkTests.swift")
print("• Learn about UI testing with XCTest")
print("• Explore test-driven development (TDD)")
print("• Set up continuous integration for automated testing")
