#!/bin/bash

# VoiceInk Test Setup Script
# This script sets up the development environment and runs unit tests for VoiceInk

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check macOS version
check_macos_version() {
    log_info "Checking macOS version..."
    
    local macos_version=$(sw_vers -productVersion)
    local major_version=$(echo $macos_version | cut -d. -f1)
    local minor_version=$(echo $macos_version | cut -d. -f2)
    
    log_info "Current macOS version: $macos_version"
    
    # Check if macOS 14.0 or later
    if [[ $major_version -gt 14 ]] || [[ $major_version -eq 14 && $minor_version -ge 0 ]]; then
        log_success "macOS version requirement met (14.0+)"
        return 0
    else
        log_error "macOS 14.0 or later is required. Current version: $macos_version"
        return 1
    fi
}

# Function to check Xcode installation
check_xcode() {
    log_info "Checking Xcode installation..."

    if ! command_exists xcodebuild; then
        log_error "xcodebuild not found. Please install Xcode."
        return 1
    fi

    # Check if Xcode is properly configured
    if xcodebuild -version >/dev/null 2>&1; then
        local xcode_version=$(xcodebuild -version | head -n1)
        log_success "Xcode found: $xcode_version"
        return 0
    else
        log_warning "Xcode is not properly configured. Only Command Line Tools are installed."
        log_info "For full functionality, please install Xcode from the Mac App Store."
        log_info "Continuing with limited functionality..."
        return 0  # Continue anyway for basic operations
    fi
}

# Function to check Swift installation
check_swift() {
    log_info "Checking Swift installation..."

    if command_exists swift; then
        local swift_version=$(swift --version | head -n1)
        log_success "Swift found: $swift_version"
        return 0
    else
        log_error "Swift not found. Please install Xcode."
        return 1
    fi
}

# Function to check cmake installation
check_cmake() {
    log_info "Checking cmake installation..."

    if command_exists cmake; then
        local cmake_version=$(cmake --version | head -n1)
        log_success "cmake found: $cmake_version"
        return 0
    else
        log_warning "cmake not found. Required for building whisper.cpp."
        log_info "Install cmake with: brew install cmake"
        log_info "Or download from: https://cmake.org/download/"
        return 1
    fi
}

# Function to setup whisper.cpp dependency
setup_whisper_cpp() {
    log_info "Setting up whisper.cpp dependency..."

    local whisper_dir="../whisper.cpp"
    local framework_path="../whisper.cpp/build-apple/whisper.xcframework"

    # Check if whisper.xcframework already exists
    if [[ -d "$framework_path" ]]; then
        log_success "whisper.xcframework already exists at $framework_path"
        return 0
    fi

    # Check if cmake is available
    if ! check_cmake; then
        log_error "cmake is required to build whisper.cpp. Please install it first."
        return 1
    fi
    
    # Clone whisper.cpp if it doesn't exist
    if [[ ! -d "$whisper_dir" ]]; then
        log_info "Cloning whisper.cpp repository..."
        git clone https://github.com/ggerganov/whisper.cpp.git "$whisper_dir"
        if [[ $? -ne 0 ]]; then
            log_error "Failed to clone whisper.cpp repository"
            return 1
        fi
    else
        log_info "whisper.cpp directory already exists"
    fi
    
    # Build the XCFramework (macOS only)
    log_info "Building whisper.xcframework for macOS (this may take several minutes)..."
    cd "$whisper_dir"

    # Copy our simplified build script
    cp "../VoiceInk-t/build_whisper_macos.sh" .
    chmod +x build_whisper_macos.sh

    # Run our simplified build script
    ./build_whisper_macos.sh

    if [[ $? -ne 0 ]]; then
        log_error "Failed to build whisper.xcframework"
        cd - >/dev/null
        return 1
    fi
    
    cd - >/dev/null
    
    if [[ -d "$framework_path" ]]; then
        log_success "whisper.xcframework built successfully"
        return 0
    else
        log_error "whisper.xcframework was not created at expected location"
        return 1
    fi
}

# Function to check if we can run xcodebuild
can_run_xcodebuild() {
    if xcodebuild -version >/dev/null 2>&1; then
        return 0
    else
        log_warning "Cannot run xcodebuild. Full Xcode installation required for building and testing."
        return 1
    fi
}

# Function to run unit tests
run_unit_tests() {
    log_info "Running unit tests..."

    if ! can_run_xcodebuild; then
        log_error "Cannot run tests without full Xcode installation."
        log_info "Please install Xcode from the Mac App Store to run tests."
        return 1
    fi

    # Clean build folder first
    log_info "Cleaning build folder..."
    xcodebuild clean -project VoiceInk.xcodeproj -scheme VoiceInk

    # Build the project
    log_info "Building VoiceInk project..."
    xcodebuild build -project VoiceInk.xcodeproj -scheme VoiceInk -destination 'platform=macOS'

    if [[ $? -ne 0 ]]; then
        log_error "Failed to build VoiceInk project"
        return 1
    fi

    log_success "Project built successfully"

    # Run unit tests
    log_info "Running unit tests..."
    xcodebuild test -project VoiceInk.xcodeproj -scheme VoiceInk -destination 'platform=macOS' -only-testing:VoiceInkTests

    if [[ $? -eq 0 ]]; then
        log_success "Unit tests completed successfully"
        return 0
    else
        log_error "Unit tests failed"
        return 1
    fi
}

# Function to run UI tests
run_ui_tests() {
    log_info "Running UI tests..."

    if ! can_run_xcodebuild; then
        log_error "Cannot run tests without full Xcode installation."
        log_info "Please install Xcode from the Mac App Store to run tests."
        return 1
    fi

    xcodebuild test -project VoiceInk.xcodeproj -scheme VoiceInk -destination 'platform=macOS' -only-testing:VoiceInkUITests

    if [[ $? -eq 0 ]]; then
        log_success "UI tests completed successfully"
        return 0
    else
        log_error "UI tests failed"
        return 1
    fi
}

# Function to run all tests
run_all_tests() {
    log_info "Running all tests..."

    if ! can_run_xcodebuild; then
        log_error "Cannot run tests without full Xcode installation."
        log_info "Please install Xcode from the Mac App Store to run tests."
        return 1
    fi

    xcodebuild test -project VoiceInk.xcodeproj -scheme VoiceInk -destination 'platform=macOS'

    if [[ $? -eq 0 ]]; then
        log_success "All tests completed successfully"
        return 0
    else
        log_error "Some tests failed"
        return 1
    fi
}

# Main function
main() {
    log_info "Starting VoiceInk test setup..."
    
    # Check system requirements
    check_macos_version || exit 1
    check_xcode  # Don't exit on Xcode issues, just warn
    check_swift || exit 1
    # cmake check is done in setup_whisper_cpp function
    
    # Setup dependencies
    setup_whisper_cpp || exit 1
    
    # Parse command line arguments
    case "${1:-all}" in
        "unit")
            run_unit_tests
            ;;
        "ui")
            run_ui_tests
            ;;
        "all")
            run_all_tests
            ;;
        "setup")
            log_success "Setup completed successfully. Dependencies are ready."
            ;;
        *)
            log_info "Usage: $0 [unit|ui|all|setup]"
            log_info "  unit  - Run only unit tests"
            log_info "  ui    - Run only UI tests"
            log_info "  all   - Run all tests (default)"
            log_info "  setup - Only setup dependencies, don't run tests"
            exit 1
            ;;
    esac
    
    if [[ $? -eq 0 ]]; then
        log_success "Script completed successfully!"
    else
        log_error "Script failed!"
        exit 1
    fi
}

# Run main function with all arguments
main "$@"
