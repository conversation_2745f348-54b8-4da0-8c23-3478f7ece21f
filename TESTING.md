# VoiceInk Testing Guide

This guide provides comprehensive instructions for setting up and running tests for the VoiceInk macOS application.

## Prerequisites

### System Requirements
- **macOS 14.0 or later** (required for VoiceInk)
- **Xcode** (latest version recommended, not just Command Line Tools)
- **Git** (for cloning dependencies)

### Verification Commands
```bash
# Check macOS version
sw_vers -productVersion

# Check Xcode installation
xcodebuild -version

# Check Swift installation
swift --version
```

## Quick Start

### Automated Setup and Testing
We provide an automated setup script that handles all dependencies and runs tests:

```bash
# Make the script executable (if not already)
chmod +x setup_tests.sh

# Run all tests (includes setup)
./setup_tests.sh

# Or run specific test types
./setup_tests.sh unit    # Unit tests only
./setup_tests.sh ui      # UI tests only
./setup_tests.sh setup   # Setup dependencies only
```

## Manual Setup

If you prefer to set up the environment manually:

### 1. Setup whisper.cpp Dependency

VoiceInk requires the whisper.xcframework from whisper.cpp:

```bash
# Clone whisper.cpp (in parent directory)
cd ..
git clone https://github.com/ggerganov/whisper.cpp.git
cd whisper.cpp

# Build the XCFramework (this takes several minutes)
./build-xcframework.sh

# Return to VoiceInk directory
cd ../VoiceInk-t
```

### 2. Verify Framework Location

Ensure the framework is at the expected location:
```bash
ls -la ../whisper.cpp/build-apple/whisper.xcframework
```

## Running Tests

### Using Xcode
1. Open `VoiceInk.xcodeproj` in Xcode
2. Select the VoiceInk scheme
3. Use `Cmd+U` to run all tests
4. Or use the Test Navigator to run specific tests

### Using Command Line

#### All Tests
```bash
xcodebuild test -project VoiceInk.xcodeproj -scheme VoiceInk -destination 'platform=macOS'
```

#### Unit Tests Only
```bash
xcodebuild test -project VoiceInk.xcodeproj -scheme VoiceInk -destination 'platform=macOS' -only-testing:VoiceInkTests
```

#### UI Tests Only
```bash
xcodebuild test -project VoiceInk.xcodeproj -scheme VoiceInk -destination 'platform=macOS' -only-testing:VoiceInkUITests
```

#### Clean Build Before Testing
```bash
xcodebuild clean -project VoiceInk.xcodeproj -scheme VoiceInk
xcodebuild test -project VoiceInk.xcodeproj -scheme VoiceInk -destination 'platform=macOS'
```

## Test Structure

### Unit Tests (`VoiceInkTests`)
- **Location**: `VoiceInkTests/VoiceInkTests.swift`
- **Framework**: Swift Testing
- **Purpose**: Test individual components and business logic
- **Current Status**: Basic test structure in place

### UI Tests (`VoiceInkUITests`)
- **Location**: `VoiceInkUITests/`
- **Framework**: XCTest
- **Purpose**: Test user interface and user interactions
- **Files**:
  - `VoiceInkUITests.swift` - Main UI tests
  - `VoiceInkUITestsLaunchTests.swift` - Launch performance tests

## Dependencies

### Swift Packages (Managed by SPM)
- **KeyboardShortcuts** (v2.3.0) - User-customizable keyboard shortcuts
- **LaunchAtLogin-Modern** (main branch) - Launch at login functionality
- **Sparkle** (v2.7.0) - App updates
- **Zip** (v2.1.2) - Archive handling

### External Frameworks
- **whisper.xcframework** - Speech recognition (built from whisper.cpp)

## Troubleshooting

### Common Issues

#### 1. Xcode Not Found
```
Error: xcode-select: error: tool 'xcodebuild' requires Xcode
```
**Solution**: Install full Xcode from Mac App Store, not just Command Line Tools.

#### 2. whisper.xcframework Missing
```
Error: Framework not found
```
**Solution**: Run the setup script or manually build whisper.cpp as described above.

#### 3. Build Failures
```
Error: Build failed
```
**Solutions**:
- Clean build folder: `xcodebuild clean`
- Verify all dependencies are properly linked
- Check Xcode and macOS versions
- Ensure whisper.xcframework is at the correct path

#### 4. Permission Issues
```
Error: Permission denied
```
**Solution**: Make sure the setup script is executable:
```bash
chmod +x setup_tests.sh
```

### Debug Commands

```bash
# List available schemes
xcodebuild -list -project VoiceInk.xcodeproj

# Check project settings
xcodebuild -showBuildSettings -project VoiceInk.xcodeproj -scheme VoiceInk

# Verbose build output
xcodebuild test -project VoiceInk.xcodeproj -scheme VoiceInk -destination 'platform=macOS' -verbose
```

## Continuous Integration

For CI/CD pipelines, use the automated setup script:

```bash
# In your CI script
./setup_tests.sh all
```

The script will:
1. Verify system requirements
2. Setup all dependencies
3. Build the project
4. Run all tests
5. Report results

## Contributing

When adding new tests:

1. **Unit Tests**: Add to `VoiceInkTests/VoiceInkTests.swift` using Swift Testing framework
2. **UI Tests**: Add to `VoiceInkUITests/VoiceInkUITests.swift` using XCTest framework
3. **Run tests locally** before submitting PRs
4. **Update documentation** if adding new test categories

## Performance Considerations

- **whisper.cpp build**: Takes 5-10 minutes on first setup
- **Test execution**: Unit tests are fast, UI tests may take longer
- **Clean builds**: Recommended when switching between branches

## Support

If you encounter issues:
1. Check this documentation
2. Review the [BUILDING.md](BUILDING.md) guide
3. Check existing GitHub issues
4. Create a new issue with detailed error information
