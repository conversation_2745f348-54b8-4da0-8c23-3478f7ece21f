#!/usr/bin/env swift

import Foundation

// Simple test framework for demonstration
struct TestResult {
    let name: String
    let passed: Bool
    let message: String?
}

class SimpleTestRunner {
    private var results: [TestResult] = []
    
    func expect(_ condition: Bool, _ message: String = "") throws {
        if !condition {
            throw TestError.expectationFailed(message)
        }
    }
    
    func run(_ name: String, test: () throws -> Void) {
        do {
            try test()
            results.append(TestResult(name: name, passed: true, message: nil))
            print("✅ \(name)")
        } catch {
            results.append(TestResult(name: name, passed: false, message: error.localizedDescription))
            print("❌ \(name): \(error.localizedDescription)")
        }
    }
    
    func printSummary() {
        let passed = results.filter { $0.passed }.count
        let total = results.count
        
        print("\n" + String(repeating: "=", count: 50))
        print("Test Summary: \(passed)/\(total) tests passed")

        if passed == total {
            print("🎉 All tests passed!")
        } else {
            print("⚠️  \(total - passed) test(s) failed")
        }
        print(String(repeating: "=", count: 50))
    }
}

enum TestError: Error, LocalizedError {
    case expectationFailed(String)
    
    var errorDescription: String? {
        switch self {
        case .expectationFailed(let message):
            return "Expectation failed: \(message)"
        }
    }
}

// Mock implementations for demonstration
class MockTranscription {
    var id: UUID
    var text: String
    var enhancedText: String?
    var timestamp: Date
    var duration: TimeInterval
    var audioFileURL: String?
    
    init(text: String, duration: TimeInterval, enhancedText: String? = nil, audioFileURL: String? = nil) {
        self.id = UUID()
        self.text = text
        self.enhancedText = enhancedText
        self.timestamp = Date()
        self.duration = duration
        self.audioFileURL = audioFileURL
    }
}

class MockWordReplacementService {
    static let shared = MockWordReplacementService()
    
    private init() {}
    
    func applyReplacements(to text: String) -> String {
        guard let replacements = UserDefaults.standard.dictionary(forKey: "wordReplacements") as? [String: String],
              !replacements.isEmpty else {
            return text
        }
        
        var modifiedText = text
        
        for (original, replacement) in replacements {
            let pattern = "\\b\(NSRegularExpression.escapedPattern(for: original))\\b"
            if let regex = try? NSRegularExpression(pattern: pattern, options: .caseInsensitive) {
                let range = NSRange(modifiedText.startIndex..., in: modifiedText)
                modifiedText = regex.stringByReplacingMatches(
                    in: modifiedText,
                    options: [],
                    range: range,
                    withTemplate: replacement
                )
            }
        }
        
        return modifiedText
    }
}

// Test implementations
let runner = SimpleTestRunner()

print("🧪 Running VoiceInk Unit Tests (Standalone Demo)")
print(String(repeating: "=", count: 50))

// Transcription Tests
runner.run("Transcription model initialization") {
    let text = "Hello, this is a test transcription."
    let duration: TimeInterval = 5.0
    let enhancedText = "Hello, this is a test transcription with enhanced formatting."
    
    let transcription = MockTranscription(
        text: text,
        duration: duration,
        enhancedText: enhancedText
    )
    
    try runner.expect(transcription.text == text, "Text should match")
    try runner.expect(transcription.duration == duration, "Duration should match")
    try runner.expect(transcription.enhancedText == enhancedText, "Enhanced text should match")
    try runner.expect(transcription.timestamp <= Date(), "Timestamp should be recent")
}

runner.run("Transcription without enhanced text") {
    let text = "Simple transcription"
    let duration: TimeInterval = 2.5
    
    let transcription = MockTranscription(text: text, duration: duration)
    
    runner.expect(transcription.text == text, "Text should match")
    runner.expect(transcription.duration == duration, "Duration should match")
    runner.expect(transcription.enhancedText == nil, "Enhanced text should be nil")
    runner.expect(transcription.audioFileURL == nil, "Audio file URL should be nil")
}

// Word Replacement Tests
runner.run("Word replacement with no replacements") {
    UserDefaults.standard.removeObject(forKey: "wordReplacements")
    
    let service = MockWordReplacementService.shared
    let originalText = "This is a test sentence."
    let result = service.applyReplacements(to: originalText)
    
    runner.expect(result == originalText, "Text should remain unchanged when no replacements")
}

runner.run("Word replacement with single replacement") {
    let replacements = ["test": "example"]
    UserDefaults.standard.set(replacements, forKey: "wordReplacements")
    
    let service = MockWordReplacementService.shared
    let originalText = "This is a test sentence."
    let expectedText = "This is a example sentence."
    let result = service.applyReplacements(to: originalText)
    
    runner.expect(result == expectedText, "Single word replacement should work")
    
    UserDefaults.standard.removeObject(forKey: "wordReplacements")
}

runner.run("Word replacement case insensitive") {
    let replacements = ["hello": "hi"]
    UserDefaults.standard.set(replacements, forKey: "wordReplacements")
    
    let service = MockWordReplacementService.shared
    let originalText = "Hello world! HELLO there. hello again."
    let expectedText = "hi world! hi there. hi again."
    let result = service.applyReplacements(to: originalText)
    
    runner.expect(result == expectedText, "Case insensitive replacement should work")
    
    UserDefaults.standard.removeObject(forKey: "wordReplacements")
}

runner.run("Word replacement whole word only") {
    let replacements = ["cat": "dog"]
    UserDefaults.standard.set(replacements, forKey: "wordReplacements")
    
    let service = MockWordReplacementService.shared
    let originalText = "The cat is in the category."
    let expectedText = "The dog is in the category."
    let result = service.applyReplacements(to: originalText)
    
    runner.expect(result == expectedText, "Should only replace whole words")
    
    UserDefaults.standard.removeObject(forKey: "wordReplacements")
}

runner.run("Word replacement with empty text") {
    let replacements = ["test": "example"]
    UserDefaults.standard.set(replacements, forKey: "wordReplacements")
    
    let service = MockWordReplacementService.shared
    let result = service.applyReplacements(to: "")
    
    runner.expect(result == "", "Empty text should remain empty")
    
    UserDefaults.standard.removeObject(forKey: "wordReplacements")
}

// Print results
runner.printSummary()

print("\n📚 What you learned:")
print("• How to structure unit tests with Swift Testing framework")
print("• Testing model initialization and properties")
print("• Testing service methods with different inputs")
print("• Testing edge cases and error conditions")
print("• Using UserDefaults in tests (with cleanup)")
print("• Mocking dependencies for isolated testing")
